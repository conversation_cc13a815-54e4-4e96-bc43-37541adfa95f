import { db } from '../db/index.js';
import { tasks, taskReminders } from '../db/schema.js';
import type { Task, NewTask, NewTaskReminder } from '../db/schema.js';
import { eq, and, gte, lt, isNull } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate recurring task instances based on recurrence rule
 */
export async function generateRecurringInstances(templateTask: Task, endDate: Date): Promise<NewTask[]> {
  console.log('generateRecurringInstances called with:');
  console.log('templateTask.recurrenceRule:', templateTask.recurrenceRule);
  console.log('templateTask.dueDate:', templateTask.dueDate);

  if (!templateTask.recurrenceRule || !templateTask.dueDate) {
    console.log('Early return: missing recurrenceRule or dueDate');
    return [];
  }

  const instances: NewTask[] = [];
  const rule = templateTask.recurrenceRule as any;
  const startDate = new Date(templateTask.dueDate);
  const recurringGroupId = templateTask.recurringGroupId || uuidv4();

  console.log('Start date:', startDate);
  console.log('End date:', endDate);
  console.log('Rule:', rule);

  let currentDate = new Date(startDate);
  let instanceCount = 0;
  const maxInstances = 100; // Safety limit

  console.log(`Loop condition: currentDate (${currentDate}) <= endDate (${endDate}) = ${currentDate <= endDate}`);
  console.log(`maxInstances: ${maxInstances}`);

  while (currentDate <= endDate && instanceCount < maxInstances) {
    console.log(`Iteration ${instanceCount}: currentDate = ${currentDate}, endDate = ${endDate}`);

    // Always create instances for recurring tasks
    const instance: NewTask = {
      userId: templateTask.userId,
      title: templateTask.title,
      notes: templateTask.notes,
      priority: templateTask.priority,
      categoryId: templateTask.categoryId,
      dueDate: new Date(currentDate),
      completed: false,
      subtasks: templateTask.subtasks,
      recurrenceRule: templateTask.recurrenceRule,
      reminderDays: templateTask.reminderDays,
      recurringGroupId: recurringGroupId,
      isRecurringTemplate: false,
      recurringInstanceDate: new Date(currentDate)
    };
    instances.push(instance);

    // Calculate next occurrence based on rule
    const nextDate = getNextOccurrence(currentDate, rule);
    console.log(`Next occurrence: ${nextDate}`);
    currentDate = nextDate;
    instanceCount++;

    // Check end conditions
    if (rule.endType === 'after' && instanceCount >= rule.endAfterOccurrences) {
      break;
    }
    if (rule.endType === 'on' && rule.endOnDate && currentDate > new Date(rule.endOnDate)) {
      break;
    }
  }

  return instances;
}

/**
 * Calculate next occurrence date based on recurrence rule
 */
function getNextOccurrence(currentDate: Date, rule: any): Date {
  const nextDate = new Date(currentDate);

  switch (rule.type) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + (rule.interval || 1));
      break;

    case 'weekly':
      if (rule.weekdays && rule.weekdays.length > 0) {
        // Find next weekday
        const currentWeekday = nextDate.getDay();
        const sortedWeekdays = [...rule.weekdays].sort((a, b) => a - b);

        let nextWeekday = sortedWeekdays.find(day => day > currentWeekday);
        if (!nextWeekday) {
          // Move to next week and use first weekday
          nextWeekday = sortedWeekdays[0];
          nextDate.setDate(nextDate.getDate() + (7 - currentWeekday + nextWeekday));
        } else {
          nextDate.setDate(nextDate.getDate() + (nextWeekday - currentWeekday));
        }
      } else {
        nextDate.setDate(nextDate.getDate() + (7 * (rule.interval || 1)));
      }
      break;

    case 'monthly':
      if (rule.monthlyType === 'date') {
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        nextDate.setDate(rule.monthlyDate || 1);
      } else if (rule.monthlyType === 'weekday') {
        // Every Nth weekday of the month
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        const targetWeekday = rule.monthlyWeekday || 1;
        const weekNumber = rule.monthlyWeekNumber || 1;

        // Set to first day of month
        nextDate.setDate(1);

        // Find first occurrence of target weekday
        while (nextDate.getDay() !== targetWeekday) {
          nextDate.setDate(nextDate.getDate() + 1);
        }

        // Add weeks to get to the Nth occurrence
        if (weekNumber === -1) {
          // Last occurrence - find last one in month
          const lastDay = new Date(nextDate.getFullYear(), nextDate.getMonth() + 1, 0);
          while (lastDay.getDay() !== targetWeekday) {
            lastDay.setDate(lastDay.getDate() - 1);
          }
          nextDate.setDate(lastDay.getDate());
        } else {
          nextDate.setDate(nextDate.getDate() + (7 * (weekNumber - 1)));
        }
      }
      break;

    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + (rule.interval || 1));
      if (rule.yearlyMonth) {
        nextDate.setMonth(rule.yearlyMonth - 1);
      }
      break;

    default:
      nextDate.setDate(nextDate.getDate() + 1);
  }

  return nextDate;
}

/**
 * Create recurring task and generate initial instances
 */
export async function createRecurringTask(taskData: Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
  const recurringGroupId = uuidv4();

  // Create template task
  const templateData: NewTask = {
    ...taskData,
    recurringGroupId,
    isRecurringTemplate: true
  };

  const [template] = await db.insert(tasks).values(templateData).returning();

  // Generate instances for next 3 months
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + 3);

  const instances = await generateRecurringInstances(template, endDate);

  console.log(`Generated ${instances.length} recurring instances for template ${template.id}`);

  if (instances.length > 0) {
    await db.insert(tasks).values(instances);
    console.log(`Inserted ${instances.length} recurring task instances`);
  }

  return template;
}

/**
 * Delete all instances of a recurring task group
 */
export async function deleteRecurringTaskGroup(recurringGroupId: string, userId: string): Promise<void> {
  await db.delete(tasks).where(
    and(
      eq(tasks.recurringGroupId, recurringGroupId),
      eq(tasks.userId, userId)
    )
  );
}

/**
 * Generate reminders for tasks
 */
export async function generateTaskReminders(taskId: string): Promise<void> {
  const [task] = await db.select().from(tasks).where(eq(tasks.id, taskId));

  if (!task || !task.dueDate || task.reminderDays === null) {
    return;
  }

  // Calculate reminder date
  const reminderDate = new Date(task.dueDate);
  reminderDate.setDate(reminderDate.getDate() - (task.reminderDays || 3));
  reminderDate.setHours(6, 0, 0, 0); // 6 AM reminder time

  // Only create reminder if it's in the future
  if (reminderDate > new Date()) {
    const reminderData: NewTaskReminder = {
      userId: task.userId,
      taskId: task.id,
      reminderDate,
      reminderType: 'upcoming',
      sent: false
    };

    await db.insert(taskReminders).values(reminderData);
  }
}

/**
 * Get tasks that need reminders today
 */
export async function getTasksNeedingReminders(date: Date = new Date()): Promise<TaskReminder[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  return await db.select()
    .from(taskReminders)
    .where(
      and(
        gte(taskReminders.reminderDate, startOfDay),
        lt(taskReminders.reminderDate, endOfDay),
        eq(taskReminders.sent, false)
      )
    );
}

/**
 * Mark reminder as sent
 */
export async function markReminderSent(reminderId: string): Promise<void> {
  await db.update(taskReminders)
    .set({ sent: true, sentAt: new Date() })
    .where(eq(taskReminders.id, reminderId));
}
