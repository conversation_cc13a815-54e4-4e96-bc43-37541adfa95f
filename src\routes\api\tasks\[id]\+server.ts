import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { getTaskById, updateTask, deleteTask, completeTask } from '$lib/server/db/operations.js';
import { deleteRecurringTaskGroup } from '$lib/server/services/recurringTasks.js';

export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const task = await getTaskById(params.id, payload.userId);
    if (!task) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    return json({ task });
  } catch (error) {
    console.error('Get task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ params, request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if task exists
    const existingTask = await getTaskById(params.id, payload.userId);
    if (!existingTask) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    const updates = await request.json();

    // Validate title if provided
    if (updates.title !== undefined && (!updates.title || updates.title.trim() === '')) {
      return json({ error: 'Title cannot be empty' }, { status: 400 });
    }

    // Validate priority if provided
    if (updates.priority !== undefined && (updates.priority < 0 || updates.priority > 2)) {
      return json({ error: 'Priority must be 0 (low), 1 (normal), or 2 (high)' }, { status: 400 });
    }

    // Parse due date if provided
    if (updates.dueDate !== undefined && updates.dueDate !== null) {
      const parsedDueDate = new Date(updates.dueDate);
      if (isNaN(parsedDueDate.getTime())) {
        return json({ error: 'Invalid due date format' }, { status: 400 });
      }
      updates.dueDate = parsedDueDate;
    }

    // Clean up string fields
    if (updates.title) updates.title = updates.title.trim();
    if (updates.subtitle) updates.subtitle = updates.subtitle.trim();
    if (updates.notes) updates.notes = updates.notes.trim();

    await updateTask(params.id, payload.userId, updates);

    // Return updated task
    const updatedTask = await getTaskById(params.id, payload.userId);
    return json({ task: updatedTask });
  } catch (error) {
    console.error('Update task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if task exists
    const existingTask = await getTaskById(params.id, payload.userId);
    if (!existingTask) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    // Check if this is a recurring task
    if (existingTask.recurringGroupId) {
      // Delete entire recurring task group
      await deleteRecurringTaskGroup(existingTask.recurringGroupId, payload.userId);
      return json({ message: 'Recurring task group deleted successfully' });
    } else {
      // Delete single task
      await deleteTask(params.id, payload.userId);
      return json({ message: 'Task deleted successfully' });
    }
  } catch (error) {
    console.error('Delete task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
